"use client";

import { useEffect, useState } from "react";
import { getPublicTherapistById, TherapistData } from "@/services/public-calendar.service";
import Image from "next/image";
import { <PERSON><PERSON> } from 'next/font/google';
import './fonts.css';
import { useRouter } from "next/navigation";

const roboto = Roboto({
  weight: ['400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
});

type TherapistProfileData = {
  _id: string;
  name: string;
  fullName: string;
  pronouns: string;
  profileImage: string;
  profilePicUrl: string;
  yearsOfExperience: number;
  location: string;
  therapyApproach: string;
  practiceApproach: string;
  values: string[];
  therapyTypes: string[];
  concerns: string[];
  qualifications?: string[];
  professionalQualification?: string;
  languages: string[];
  minFee: number;
  maxFee: number;
  designation: string;
};

interface VerificationDetails {
  docs?: string[];
  featuresNeed?: string[];
  genderPreference?: string[];
  practicingTitle?: string;
  sentForVerification?: boolean;
  source?: string[];
  uploadedDocsCount?: number;
  yearsOfExperience?: string;
}

export default function TherapistProfilePage({ params }: { params: { id: string } }) {
  const [therapistData, setTherapistData] = useState<TherapistProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const { id } = params;

  useEffect(() => {
    const fetchTherapistData = async () => {
      try {
        setIsLoading(true);

        // Use the public endpoint that doesn't require authentication
        let response;
        try {
          // Make the API call to get the therapist data
          response = await getPublicTherapistById(id);

          // The API returns the therapist data directly without a wrapper
        // So we just need to check if the response is an object
        if (!response || typeof response !== 'object') {
            console.error("Invalid API response:", response);
            setError("Unable to load therapist profile. The API returned an invalid response.");
            setIsLoading(false);
            return;
          }
        } catch (fetchError) {
          console.error("Error fetching therapist profile data:", fetchError);
          setError("Unable to load therapist profile. Please check the identifier and try again.");
          setIsLoading(false);
          return;
        }

        const apiData = response as unknown as TherapistData & {
          verificationDetails?: VerificationDetails;
        };

        // Helper functions to safely handle potentially undefined or null values
        const safeString = (value: string | Record<string, unknown> | null | undefined): string => {
          if (typeof value === 'string') return value;
          return '';
        };

        const safeNumber = (value: number | string | null | undefined): number => {
          if (typeof value === 'number') return value;
          if (typeof value === 'string') {
            const parsed = parseFloat(value);
            return isNaN(parsed) ? 0 : parsed;
          }
          return 0;
        };

        const safeStringArray = (value: string[] | null | undefined): string[] => {
          if (Array.isArray(value)) return value;
          return [];
        };

        // Format location
        let locationValue = '';
        if (apiData.location) {
          if (Array.isArray(apiData.location)) {
            locationValue = apiData.location.join(', ');
          } else if (typeof apiData.location === 'string') {
            locationValue = apiData.location;
          }
        }

        try {
          // Use the profile image URL from the API with better fallback logic
          let profileImageUrl = safeString(apiData.profilePicUrl);

          // If no profile image or it's not a valid URL, use static fallback
          if (!profileImageUrl || profileImageUrl === 'null' || profileImageUrl === 'undefined') {
            profileImageUrl = "/assets/images/newHome/therapist-profile-logo.png";
          } else if (!profileImageUrl.startsWith('http')) {
            // If it's a relative path, make it absolute
            const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://app.thoughtpudding.com';
            profileImageUrl = profileImageUrl.startsWith('/') ? `${baseUrl}${profileImageUrl}` : `${baseUrl}/${profileImageUrl}`;
          }

          const verificationDetails = apiData.verificationDetails || {};
          const practicingTitle = verificationDetails?.practicingTitle;
          const yearsOfExperience = verificationDetails?.yearsOfExperience;

          const formattedData: TherapistProfileData = {
            _id: apiData._id || "",
            name: safeString(apiData.name),
            fullName: safeString(apiData.name),
            pronouns: safeString(apiData.pronouns),
            profileImage: profileImageUrl,
            profilePicUrl: profileImageUrl,
            yearsOfExperience:
              safeNumber(yearsOfExperience) ||
              safeNumber(apiData.yearsOfExperience),
            location: locationValue,
            therapyApproach: safeString(apiData.therapyApproach as string | Record<string, unknown> | null | undefined),
            practiceApproach: safeString(apiData.practiceApproach),
            values: safeStringArray(apiData.values),
            therapyTypes: safeStringArray(apiData.therapyTypes),
            concerns: safeStringArray(apiData.concerns),
            professionalQualification: safeString(apiData.professionalQualification),
            languages: safeStringArray(apiData.languages),
            minFee: safeNumber(apiData.minFee),
            maxFee: safeNumber(apiData.maxFee),
            designation:
              safeString(practicingTitle) || safeString(apiData.designation),
          };
          setTherapistData(formattedData);
        } catch (formattingError) {
          console.error("Error formatting therapist data:", formattingError);
          setError("Unable to process therapist profile data.");
        }
      } catch (error) {
        // Log detailed errors
        console.error("Error in therapist data processing:", error);

        // If it's an Axios error, try to extract more detailed information
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as { response?: { data: unknown; status: number } };
          if (axiosError.response) {
            console.error('Error response data:', axiosError.response.data);
            console.error('Error response status:', axiosError.response.status);
          }
        }

        // Show a user-friendly error message
        setError("Unable to load therapist profile. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchTherapistData();
  }, [id]);

  if (isLoading) {
    return (
      <div
        className={`bg-[#2A1B6D] min-h-screen text-white flex flex-col items-center justify-center p-4 md:p-6 ${roboto.className}`}
        style={{
          backgroundImage: `url('/assets/images/newHome/bg-carousal.png')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundBlendMode: 'overlay',
          backgroundAttachment: 'fixed'
        }}
      >
        <div className="flex justify-center mb-6">
          <Image
            src="/assets/images/newHome/therapist-profile-logo.png"
            alt="Thought Pudding Logo"
            width={150}
            height={40}
            className="h-auto"
          />
        </div>
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white mb-4"></div>
          <p className="text-xl font-medium text-white">Loading therapist profile...</p>
        </div>


      </div>
    );
  }

  if (error || !therapistData) {
    return (
      <div
        className={`bg-[#2A1B6D] min-h-screen text-white flex flex-col items-center justify-center p-4 md:p-6 ${roboto.className}`}
        style={{
          backgroundImage: `url('/assets/images/newHome/bg-carousal.png')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundBlendMode: 'overlay',
          backgroundAttachment: 'fixed'
        }}
      >
        <div className=" text-black p-8 rounded-xl shadow-lg max-w-md w-full">
          <div className="flex justify-center mb-6">
            <Image
              src="/assets/images/newHome/therapist-profile-logo.png"
              alt="Thought Pudding Logo"
              width={150}
              height={40}
              className="h-auto"
            />
          </div>
          <div className="text-center">
            <h2 className="text-xl font-semibold text-red-600 mb-4">Unable to Load Profile</h2>
            <p className="text-black mb-6">{error || "We couldn't find the therapist profile you're looking for. Please check the URL and try again."}</p>
          </div>
        </div>


      </div>
    );
  }

  return (
      <div
        className={`bg-[#2A1B6D] min-h-screen text-white pb-[90px] md:pb-0 p-4 md:p-6 ${roboto.className} relative`}
        style={{
          backgroundImage: `url('/assets/images/newHome/bg-carousal.png')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundBlendMode: 'overlay',
          backgroundAttachment: 'fixed'
        }}
      >
        <div className="w-full mx-auto md:max-w-[1400px] md:px-[30px] md:my-8 md:mx-auto md:rounded-2xl md:overflow-hidden md:bg-[#2A1B6D]/80">
          {/* Mobile Logo - Only visible on mobile at top left */}
          <div className="flex justify-start mt-0 mb-4 items-center sm:hidden">
            <p className="text-sm text-white mr-2">Powered by</p>
            <Image
              src="/assets/images/newHome/therapist-profile-logo.png"
              alt="Thought Pudding"
              width={100}
              height={25}
            />
          </div>

          {/* Desktop Layout - Hidden on Mobile */}
          <div className="hidden md:grid md:grid-cols-12 gap-4 auto-rows-auto">
            {/* Profile Image */}
            <div className="col-span-3 row-span-3 rounded-xl overflow-hidden shadow-lg">
              <div className="relative w-full h-full min-h-[600px]">
                <Image
                  src={therapistData.profileImage}
                  alt={therapistData.fullName}
                  fill
                  sizes="25vw"
                  className="object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "/assets/images/newHome/therapist-profile-logo.png";
                  }}
                />
              </div>
            </div>

            {/* Basic Info */}
            <div className="p-6 col-span-7 bg-white text-black rounded-xl flex flex-col justify-between min-h-[200px]">
              <div className="flex flex-row justify-between items-center gap-3">
                <div>
                  <h2 className="text-[36px] font-bold text-[#6D84FF] gilmer-bold">
                    {therapistData.fullName}{" "}
                    <span className="text-[24.54px] text-black font-normal gilmer-regular">
                      ({therapistData.pronouns})
                    </span>
                  </h2>
                  <p className="text-[16px] text-gray-700">{therapistData.designation}</p>
                </div>
                <div className="flex flex-wrap gap-2">
                  <div className="bg-[#718FFF] text-white px-3 py-1 rounded-md text-sm font-medium">
                    {therapistData.yearsOfExperience}+ year of experience
                  </div>
                  <div className="text-[16px] text-black">
                    ({therapistData.location})
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <h3 className="font-semibold text-[20px] gilmer-bold">What therapy looks like with me</h3>
                <p className="text-[18px] text-gray-600 mt-1">{therapistData.practiceApproach}</p>
              </div>
            </div>

            {/* Languages */}
            <div className="col-span-2 bg-white text-black p-4 rounded-xl min-h-[200px]">
              <h4 className="font-semibold text-[24px] mb-3 gilmer-bold">Languages</h4>
              <div className="flex flex-col gap-2 justify-start">
                {therapistData.languages.map((lang, idx) => (
                  <span key={idx} className="text-center bg-[#718FFF] text-white px-3 py-1 rounded-md text-[17px]">
                    {lang}
                  </span>
                ))}
              </div>
            </div>

            {/* Values */}
            <div className="col-span-3 bg-[#6D84FF] p-4 rounded-xl min-h-[180px]">
              <h4 className="font-semibold text-white text-[24px] mb-3 gilmer-bold">Values I Align With</h4>
              <ul className="text-white space-y-2 text-[18px] gilmer-medium">
                {therapistData.values.map((value, idx) => (
                  <li key={idx} className="flex items-center">
                    <span className="mr-2 text-[28px]">•</span>{value}
                  </li>
                ))}
              </ul>
            </div>

            {/* Therapy Types */}
            <div className="col-span-3 bg-white text-black p-4 rounded-xl min-h-[180px]">
              <h4 className="font-semibold text-[28px] mb-3 gilmer-bold">Types of Therapy</h4>
              <ul className="space-y-2 text-[20px] gilmer-medium">
                {therapistData.therapyTypes.map((type, idx) => (
                  <li key={idx} className="flex items-center">
                    <span className="mr-2 text-[28px]">•</span>{type}
                  </li>
                ))}
              </ul>
            </div>

            {/* Concerns */}
            <div className="col-span-3 row-span-2 bg-[#6D84FF] p-4 rounded-xl min-h-[360px]">
              <h4 className="font-semibold text-white text-[28px] mb-3 gilmer-bold">Concerns I Work With</h4>
              <ul className="text-white space-y-2 text-[20px] gilmer-medium">
                {therapistData.concerns.map((concern, idx) => (
                  <li key={idx} className="flex items-center">
                    <span className="mr-2 text-[28px]">•</span>{concern}
                  </li>
                ))}
              </ul>
            </div>

            {/* Qualifications */}
            <div className="col-span-6 bg-white text-[#251D5C] p-4 rounded-xl min-h-[160px]">
              <div className="flex items-center gap-8 justify-center h-full">
                <h4 className="font-semibold max-w-[203px] text-[30px] mb-3 gilmer-bold text-center">Qualification & Training</h4>
                <ul className="text-[20px] gilmer-medium">
                  <li className="flex items-center">
                    <span className="mr-2 text-[28px]">•</span>{therapistData.professionalQualification}
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Mobile Layout - Hidden on Desktop */}
          <div className="md:hidden">
            {/* Profile Image */}
            <div className="rounded-xl overflow-hidden shadow-lg mb-4">
              <div className="relative w-full h-[300px]">
                <Image
                  src={therapistData.profileImage}
                  alt={therapistData.fullName}
                  fill
                  sizes="100vw"
                  className="object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "/assets/images/newHome/therapist-profile-logo.png";
                  }}
                />
              </div>
            </div>

            {/* Basic Info */}
            <div className="p-4 bg-white text-black rounded-xl flex flex-col justify-between mb-4">
              <div className="flex flex-col justify-between items-start gap-3">
                <div>
                  <h2 className="text-[24px] font-bold text-[#6D84FF] gilmer-bold">
                    {therapistData.fullName}{" "}
                    <span className="text-[18px] text-black font-normal gilmer-regular">
                      ({therapistData.pronouns})
                    </span>
                  </h2>
                  <p className="text-[14px] text-gray-700">{therapistData.designation}</p>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  <div className="bg-[#718FFF] text-white px-3 py-1 rounded-md text-sm font-medium">
                    {therapistData.yearsOfExperience}+ year of experience
                  </div>
                  <div className="text-[14px] text-black">
                    ({therapistData.location})
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <h3 className="font-semibold text-[18px] gilmer-bold">What therapy looks like with me</h3>
                <p className="text-[16px] text-gray-600 mt-1">{therapistData.practiceApproach}</p>
              </div>
            </div>

            {/* Values and Types of Therapy in a single row */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              {/* Values */}
              <div className="bg-[#6D84FF] p-4 rounded-xl">
                <h4 className="font-semibold text-white text-[18px] mb-3 gilmer-bold">Values I Align With as a Therapist</h4>
                <ul className="text-white space-y-2 text-[14px] gilmer-medium">
                  {therapistData.values.map((value, idx) => (
                    <li key={idx} className="flex items-center">
                      <span className="mr-2 text-[28px]">•</span>{value}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Therapy Types */}
              <div className="bg-white text-black p-4 rounded-xl">
                <h4 className="font-semibold text-[18px] mb-3 gilmer-bold">Types of Therapy</h4>
                <ul className="space-y-2 text-[14px] gilmer-medium">
                  {therapistData.therapyTypes.map((type, idx) => (
                    <li key={idx} className="flex items-center">
                      <span className="mr-2 text-[28px]">•</span>{type}
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Languages and Concerns in a single row - 3 column grid */}
            <div className="grid grid-cols-3 gap-4 mb-4">
              {/* Languages - 1 column */}
              <div className="bg-white text-black p-4 rounded-xl">
                <h4 className="font-semibold text-[16px] mb-3 gilmer-bold">Languages</h4>
                <div className="flex flex-col gap-2">
                  {therapistData.languages.map((lang, idx) => (
                    <span key={idx} className="text-center bg-[#718FFF] text-white px-3 py-1 rounded-md text-[14px]">
                      {lang}
                    </span>
                  ))}
                </div>
              </div>

              {/* Concerns - 2 columns */}
              <div className="bg-white p-4 rounded-xl col-span-2">
                <h4 className="font-semibold text-black text-[16px] mb-3 gilmer-bold">Concerns I work with</h4>
                <ul className="text-black space-y-2 text-[14px] gilmer-medium">
                  {therapistData.concerns.map((concern, idx) => (
                    <li key={idx} className="flex items-center">
                      <span className="mr-2 text-[28px]">•</span>{concern}
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Qualifications in a single row */}
            <div className="bg-[#6D84FF] text-white p-4 rounded-xl mb-4">
              <div className="flex items-center">
                <h4 className="font-semibold text-[16px] gilmer-bold">Qualification & Training :</h4>
                <ul className="ml-4 text-[14px] gilmer-medium">
                  <li className="flex items-center">
                    <span className="mr-2 text-[28px]">•</span>{therapistData.professionalQualification}
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Desktop Fees Block - Hidden on mobile */}
          <div className="hidden md:flex md:flex-row items-center justify-between bg-white text-black rounded-xl p-4 mt-4">
            <p className="text-[32px] text-[#251D5C] font-bold gilmer-bold">
              Fees: ₹{therapistData.minFee} - ₹{therapistData.maxFee}
              <span className="text-[17px] font-medium text-[#251D5C] gilmer-regular"> (Per Session)</span>
            </p>
            <button
              onClick={() => {
                // Clear the bookingCompleted flag before starting a new booking flow
                localStorage.removeItem("bookingCompleted");
                router.push(`/clients/${id}/slot-selection`);
              }}
              className="bg-[#251D5C] text-white w-full max-w-[184px] max-h-[65px] px-6 py-2 rounded-md hover:opacity-90 font-medium">
              Connect
            </button>
          </div>

          {/* Mobile Fees Block - Fixed at bottom */}
          <div className="fixed md:hidden bottom-0 left-0 right-0 w-full z-10 bg-white text-black py-3 px-4 shadow-[0_-1px_4px_rgba(0,0,0,0.1)]">
            <div className="flex items-center justify-between">
              <p className="text-[16px] text-[#251D5C] font-bold gilmer-bold">
                Fees: ₹{therapistData.minFee} - ₹{therapistData.maxFee} <span className="text-[12px] font-medium text-[#251D5C] gilmer-regular">(Per Session)</span>
              </p>
              <button
                onClick={() => {
                  // Clear the bookingCompleted flag before starting a new booking flow
                  localStorage.removeItem("bookingCompleted");
                  router.push(`/clients/${id}/slot-selection`);
                }}
                className="bg-[#251D5C] w-full max-w-[141px] max-h-[37px] text-white px-5 py-2 rounded-md hover:opacity-90 font-medium">
                Connect
              </button>
            </div>
          </div>

          {/* Footer Logo - Only visible on desktop */}
          <div className="hidden md:flex justify-end mt-4 mb-2 items-center">
            <p className="text-sm text-white mr-2">Powered by</p>
            <Image
              src="/assets/images/newHome/therapist-profile-logo.png"
              alt="Thought Pudding"
              width={120}
              height={30}
              className="h-auto"
            />
          </div>
        </div>
      </div>
    );
}
