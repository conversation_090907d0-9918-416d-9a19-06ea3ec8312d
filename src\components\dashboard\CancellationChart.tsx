import {
  ArcElement,
  Chart as ChartJS,
  Legend,
  Tooltip,
  ChartOptions,
} from "chart.js";
import { useRef, useMemo } from "react";
import { Doughnut } from "react-chartjs-2";

ChartJS.register(ArcElement, Tooltip, Legend);

interface SessionData {
  name: string;
  session: number;
}

interface CancellationData {
  cancelled_sessions: SessionData[];
  total_cancelled_sessions: number;
  collected_fees: number;
  pending_fees: number;
}

interface DoughnutChartProps {
  cancellationData: CancellationData;
  options?: unknown;
}

const CancellationChart: React.FC<DoughnutChartProps> = ({
  cancellationData,
}) => {
  const chartRef = useRef(null);

  const cancelledSessions = cancellationData?.cancelled_sessions || [];

  const data = useMemo(() => {
    const label = cancelledSessions.map((item) => item.name);
    const sessions = cancelledSessions.map((item) => item.session);

    return {
      label,
      datasets: [
        {
          data: sessions,
          backgroundColor: ["#97DFFF", "#C68CFF", "#FFC59B", "#FBBB5E"],
          borderWidth: 0,
        },
      ],
    };
  }, [cancelledSessions]);

  const totalCancelledSessions =
    cancellationData?.total_cancelled_sessions || 0;

  const options: ChartOptions<"doughnut"> = {
    responsive: true,
    cutout: "70%",
    plugins: {
      legend: {
        position: "top" as const,
      },
      tooltip: {
        enabled: false,
        external: function (context) {
          let tooltipEl = document.getElementById("chartjs-tooltip");

          if (!tooltipEl) {
            tooltipEl = document.createElement("div");
            tooltipEl.id = "chartjs-tooltip";
            // tooltipEl.style.opacity = "0";
            tooltipEl.style.display = "none";
            tooltipEl.style.position = "absolute";
            tooltipEl.style.pointerEvents = "none";
            // tooltipEl.style.transition = "opacity 0.5s ease";
            tooltipEl.style.zIndex = "999";
            document.body.appendChild(tooltipEl);
          }

          const tooltipModel = context.tooltip;
          if (tooltipModel.opacity === 0) {
            // tooltipEl.style.opacity = "0";
            tooltipEl.style.display = "none";
            return;
          }

          if (tooltipModel.body) {
            const index = tooltipModel.dataPoints[0].dataIndex;
            const label = data.label[index];
            const count = data.datasets[0].data[index];

            const innerHtml = `
              <div class="p-2.5 bg-white rounded shadow-[0px_4px_15px_0px_#00000040]">
                <p class="text-xs_18 text-primary capitalize">${label}</p>
                <p class="pt-2.5 text-sm font-medium text-primary">${count} session cancelled</p>
              </div>
            `;

            tooltipEl.innerHTML = innerHtml;
          }

          const position = context.chart.canvas.getBoundingClientRect();
          // tooltipEl.style.opacity = "1";
          tooltipEl.style.display = "block";
          tooltipEl.style.left =
            position.left + window.pageXOffset + tooltipModel.caretX + "px";
          tooltipEl.style.top =
            position.top + window.pageYOffset + tooltipModel.caretY + "px";
        },
      },
    },
  };

  return (
    <div className="relative">
      <Doughnut
        ref={chartRef}
        data={data}
        options={options}
        className="!w-full !h-full relative"
      />
      <div className="w-48 h-48 absolute top-[51.5%] left-1/2 -translate-x-[49%] -translate-y-1/2 sm:border-2 border-[#BABABA] border-dashed rounded-full text-center flex justify-center items-center">
        <div>
          <h3 className="text-xl/7 text-black font-semibold">
            {totalCancelledSessions}
          </h3>
          <p className="text-sm/7 text-black font-medium">Sessions cancelled</p>
        </div>
      </div>
    </div>
  );
};

export default CancellationChart;
