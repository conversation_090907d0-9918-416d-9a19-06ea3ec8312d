"use client";
import { useState, useRef, useEffect, Fragment } from "react";
import { Caveat } from "next/font/google";
import { PiArrowBendUpRight, PiArrowArcLeftLight } from "react-icons/pi";
import { useRouter, useSearchParams } from "next/navigation";
import Login from "@/components/common/Login";

const caveat = Caveat({ subsets: ["latin"], weight: "700" });

const HeroSection: React.FC = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const router = useRouter();
  const queryParams = useSearchParams();
  const [loginOpen, setLoginOpen] = useState(false);
  const [th_id, setTherapistId] = useState<string | null>(null);
  const [userStep, setUserStep] = useState<string | null>(null);

  const therapist_id = queryParams.get("q");
  const user_step = queryParams.get("step");
  const loginParam = queryParams.get("login");

  useEffect(() => {
    if (therapist_id && user_step) {
      setTherapistId(therapist_id);
      setUserStep(user_step);
      setLoginOpen(true);
    }
  }, [therapist_id, user_step]);

  useEffect(() => {
    if (loginParam) {
      setLoginOpen(true);
    }
  }, [loginParam]);

  // Add iOS detection
  const isIOS = () => {
    if (typeof window !== "undefined") {
      return /iPad|iPhone|iPod/.test(navigator.userAgent) ||
             (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    }
    return false;
  };

  useEffect(() => {
    if (videoRef.current && isIOS()) {
      videoRef.current.muted = true;
      videoRef.current.load();
      videoRef.current.poster = "/assets/images/newHome/video-frame.jpg"; // Use the video frame for iOS
    }
  }, []);

  const toggleVideo = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.muted = false;
        videoRef.current.play().catch(error => {
          console.log("Video play failed:", error);
        });
      }
      setIsPlaying(!isPlaying);
    }
  };


  return (
    <Fragment>
    <section className="flex flex-col lg:flex-row items-center justify-between gap-12 py-10 m-0">
      {/* Left Side - Content */}
      <div
        className="w-full lg:w-[824px] h-auto flex flex-col items-center lg:items-start text-center lg:text-left mx-auto"
        style={{ margin: "0" }}
      >
        <h1 className="text-[32px] sm:text-[40px] md:text-[50px] font-semibold text-[#718FFF] relative leading-[110%]">
          All-in-one support for your{" "}
          <span className="md:hidden inline">
            {/* Show on small screens only: inline "Private Practice" styled */}
            <span className="relative inline-block">
              <span
                className="absolute inset-0 bg-cover bg-center rotate-2"
                style={{
                  backgroundImage:
                    "url('/assets/images/newHome/Hero-Title-back.png')",
                  width: "110%",
                  height: "100%",
                }}
              ></span>
              <span
                className={`relative font-medium text-[42px] sm:text-[52px] text-[#222222] leading-[100%] ${caveat.className}`}
              >
                Private Practice
              </span>
            </span>
          </span>
          <br className="hidden md:block" />
        </h1>

        <div className="mt-2 relative inline-block hidden md:inline-block">
          <span
            className="absolute inset-0 bg-cover bg-center rotate-2"
            style={{
              backgroundImage:
                "url('/assets/images/newHome/Hero-Title-back.png')",
              width: "110%",
              height: "100%",
            }}
          ></span>
          <span
            className={`relative font-medium text-[60px] text-[#222222] leading-[100%] ${caveat.className}`}
          >
            Private Practice
          </span>
        </div>

        <p className="text-sm sm:text-base md:text-lg font-medium text-black mt-6">
          From intake to income - manage your entire private practice. All in
          one place.
        </p>
        <p className="text-sm sm:text-base md:text-lg pt-2 font-medium text-black mt-6">
          Built for therapists,{" "}
          <span className="text-[#E36FFC]">by therapists.</span>
        </p>
        <div className="mt-8 flex gap-6">
          <button onClick={() => setLoginOpen(true)} className="text-[13px] md:text-[20px] px-6 py-3 bg-[#251D5C] text-white rounded-lg">
            Start Free Trial
          </button>
          <button
            className="text-[13px] md:text-[20px] px-6 py-3 border border-[#251D5C] text-[#251D5C] rounded-lg"
            onClick={() =>
              router.push(
                "https://calendly.com/thoughtpuddingdemo/making-private-practice-easy-demo-call"
              )
            }
          >
            Schedule Demo
          </button>
        </div>
      </div>

      {/* Right Side - Video with Styled Border */}
      <div className="relative w-[300px] sm:w-[360px] md:w-[472px] max-w-full">
        <div
          className="absolute inset-0 border-r-[0.5vw] border-b-[0.5vw] border-[#718FFF] rounded-[50px] pointer-events-none"
          style={{
            boxShadow: "8px 8px 12px rgba(113, 143, 255, 0.6)",
          }}
        ></div>

        {!isPlaying && (
          <>
            <div className="hidden lg:flex absolute left-[-100px] bottom-[50px] flex-col items-center text-black scale-110">
              <div
                className="animate-bounce"
                style={{ transform: "rotate(90deg)" }}
              >
                <PiArrowBendUpRight size={50} />
              </div>
              <span className={`text-xl ${caveat.className} text-[#E36FFC]`}>
                Click to Play
              </span>
            </div>

            <div className="lg:hidden absolute right-6 top-[-40px] flex flex-row items-center text-black scale-125 gap-2 pt-2 pb-2">
              <div className="animate-left-right">
                <PiArrowArcLeftLight size={20} />
              </div>
              <span className={`text-sm ${caveat.className} text-[#E36FFC]`}>
                Click to Play
              </span>
            </div>
          </>
        )}
        <div className="overflow-hidden rounded-[50px]">
        <video
          ref={videoRef}
          src="/assets/images/newHome/Hero-Video.mp4"
          className="w-full h-auto rounded-[50px]"
          onClick={toggleVideo}
          loop
          playsInline
          preload="auto"
          muted
          poster={isIOS() ? "/assets/images/newHome/video-frame.jpg" : undefined}
          style={{
            objectFit: 'cover',
            WebkitAppearance: 'none'
          }}
        />
        </div>
      </div>
      <Login
        setLoginOpen={setLoginOpen}
        loginOpen={loginOpen}
        th_id={th_id}
        userStep={userStep}
      />
    </section>
    </Fragment>
  );
};

export default HeroSection;
