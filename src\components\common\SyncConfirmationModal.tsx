import React, { useEffect, useState } from "react";
import <PERSON>actD<PERSON> from "react-dom";
import Button from "./Button";
import { IoCloseCircleOutline } from "react-icons/io5";

interface SyncConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  loading?: boolean;
}

const SyncConfirmationModal: React.FC<SyncConfirmationModalProps> = ({
  open,
  onClose,
  onConfirm,
  loading = false,
}) => {
  const [container, setContainer] = useState<HTMLDivElement | null>(null);

  useEffect(() => {
    if (typeof document !== "undefined") {
      // Ensure that the container exists in the DOM
      const portalContainer = document.getElementById("__next");
      if (!portalContainer) {
        const newContainer = document.createElement("div");
        newContainer.id = "portal-root";
        document.body.appendChild(newContainer);
        setContainer(newContainer);
      } else {
        setContainer(portalContainer as HTMLDivElement);
      }
    }
  }, []);

  useEffect(() => {
    if (typeof document !== "undefined" && open) {
      const scrollbarElement = document.body;

      // Hide scrollbar when modal is open
      if (scrollbarElement.style.overflow !== "hidden") {
        scrollbarElement.setAttribute("style", "overflow: hidden");
      }

      // Close modal on 'Esc' key press
      const handleEscKeyPress = (event: KeyboardEvent) => {
        if (event.key === "Escape" && open) {
          onClose(); // Close modal
        }
      };

      document.addEventListener("keydown", handleEscKeyPress);

      // Cleanup the event listener and restore the scrollbar
      return () => {
        document.removeEventListener("keydown", handleEscKeyPress);
      };
    }
  }, [open, onClose]);

  if (!container || !open) {
    return null;
  }

  return ReactDOM.createPortal(
    <div
      className={`fixed inset-0 z-[99999] flex items-center justify-center bg-gray-800 bg-opacity-50 transition-opacity duration-300 ${
        open ? "opacity-100" : "opacity-0 pointer-events-none"
      }`}
      onClick={onClose}
    >
      <div
        className="bg-white rounded-md shadow-lg p-4 transform transition-transform duration-300 overflow-y-auto mx-4 my-6 sm:my-8 w-full md:w-3/4 lg:w-3/5 2xl:w-2/5 max-w-[90%] md:max-w-[75%] lg:max-w-[60%] 2xl:max-w-[40%] max-h-[90vh] sm:max-h-[85vh]"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close button */}
        <button
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-xl font-bold"
          onClick={onClose}
          aria-label="Close modal"
        >
          <IoCloseCircleOutline className="w-8 h-8" />
        </button>

        <div className="pt-8 pb-4 px-2 sm:px-4">
          <div className="p-5">
            <h2 className="text-xl font-semibold mb-4">Just a Note 🗓️</h2>
            <p className="text-base text-gray-700 mb-6">
              We will grab new sessions from your Google Calendar, but any edits like rescheduling or deleting need to be done in the dashboard.
              <br />
              <span className="text-sm text-gray-600 mt-2 block">Appreciate your help!</span>
            </p>
            <div className="flex justify-center">
              <Button
                onClick={onConfirm}
                className="px-4 py-2 text-white rounded"
                disabled={loading}
              >
                {loading ? "Processing..." : "Got it, continue with sync"}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>,
    container
  );
};

export default SyncConfirmationModal;
