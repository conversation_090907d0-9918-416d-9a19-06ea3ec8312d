"use client";
import React, { useState, useRef, useEffect } from "react";

interface CheckboxMultiSelectProps {
  options: string[];
  selectedValues: string[];
  onChange: (values: string[]) => void;
  placeholder: string;
  error?: string;
  className?: string;
}

const CheckboxMultiSelect: React.FC<CheckboxMultiSelectProps> = ({
  options,
  selectedValues,
  onChange,
  placeholder,
  error,
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownDirection, setDropdownDirection] = useState<'down' | 'up'>('down');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Calculate dropdown direction when opening
  useEffect(() => {
    if (isOpen && inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const dropdownHeight = 240; // max-h-60 = 240px
      const spaceBelow = viewportHeight - rect.bottom;
      const spaceAbove = rect.top;

      // If there's not enough space below but enough space above, open upward
      if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
        setDropdownDirection('up');
      } else {
        setDropdownDirection('down');
      }
    }
  }, [isOpen]);

  // Toggle a value in the selection
  const toggleValue = (value: string) => {
    if (selectedValues.includes(value)) {
      onChange(selectedValues.filter((item) => item !== value));
    } else {
      onChange([...selectedValues, value]);
    }
  };

  // Remove a selected value
  const removeValue = (value: string, e: React.MouseEvent) => {
    e.stopPropagation();
    onChange(selectedValues.filter((item) => item !== value));
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef} style={{ zIndex: isOpen ? 50 : 'auto' }}>
      {/* Input field that shows selected values */}
      <div
        ref={inputRef}
        className={`w-full p-3 border ${
          error ? "border-red-500" : isOpen ? "border-blue-500" : "border-gray-300"
        } rounded-lg cursor-pointer flex items-start bg-white min-h-[48px]`}
        onClick={() => setIsOpen(!isOpen)}
      >
        {selectedValues.length > 0 ? (
          <div className="flex flex-wrap gap-1 flex-1 mr-2">
            {selectedValues.map((value) => (
              <span
                key={value}
                className="px-2 py-1 rounded-full text-xs bg-gray-200 text-gray-800 flex items-center mb-1 max-w-full"
                style={{ wordBreak: 'break-word', whiteSpace: 'normal' }}
              >
                <span className="truncate max-w-[180px]" title={value}>
                  {value}
                </span>
                <button
                  type="button"
                  className="ml-1 text-gray-600 hover:text-gray-800 focus:outline-none flex-shrink-0"
                  onClick={(e) => removeValue(value, e)}
                  aria-label={`Remove ${value}`}
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        ) : (
          <span className="text-gray-500 flex-1">{placeholder}</span>
        )}
        <div className="flex-shrink-0 ml-2 self-start mt-1">
          <svg
            className={`w-5 h-5 ${isOpen ? "text-blue-500" : "text-gray-400"} transition-transform ${
              isOpen ? "transform rotate-180" : ""
            }`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            ></path>
          </svg>
        </div>
      </div>

      {/* Dropdown menu with checkboxes */}
      {isOpen && (
        <div
          className={`absolute z-50 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto ${
            dropdownDirection === 'up'
              ? 'bottom-full mb-1'
              : 'top-full mt-1'
          }`}
          style={{ minWidth: '100%', maxWidth: '400px' }}
        >
          {options.length > 0 ? (
            options.map((option) => (
              <div
                key={option}
                className={`flex items-start px-4 py-3 hover:bg-blue-50 cursor-pointer ${
                  selectedValues.includes(option) ? "bg-blue-50" : ""
                }`}
                onClick={() => toggleValue(option)}
              >
                <input
                  type="checkbox"
                  className="mr-3 h-4 w-4 accent-blue-600 cursor-pointer flex-shrink-0 mt-0.5"
                  checked={selectedValues.includes(option)}
                  onChange={(e) => {
                    // Handle checkbox click directly
                    e.stopPropagation();
                    toggleValue(option);
                  }}
                />
                <span className="text-sm leading-relaxed break-words">{option}</span>
              </div>
            ))
          ) : (
            <div className="px-4 py-3 text-gray-500 text-center">No options available</div>
          )}
        </div>
      )}

      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
    </div>
  );
};

export default CheckboxMultiSelect;
