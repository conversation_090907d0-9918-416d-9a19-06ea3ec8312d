"use client";
import { useEffect, useState } from "react";
import { Caveat } from "next/font/google";
import { useSearchParams } from "next/navigation";
import Login from "@/components/common/Login";

const caveat = Caveat({ subsets: ["latin"], weight: "700" });

const tabs = [
  {
    title: "Seamless Scheduling",
    color: "bg-[#708CFF]",
    hexColor: "#708CFF",
    content: [
      "Sync old & new sessions, no double-booking.",
      "Manage sessions, cancellations, and reschedules in one-click",
      "Sync all your sessions in a minute- no need to start from scratch",
    ],
    image: "/assets/images/newHome/scheduling.png",
  },
  {
    title: "Effortless Payment",
    color: "bg-[#D7FF7B]",
    hexColor: "#D7FF7B",
    content: [
      "Monitor income, cancellations & send one-click reminders.",
      "Bye bye to excel hassle, track your all your payments from first to the last session ",
      "No more missing out on cancellations and no-shows",
    ],
    image: "/assets/images/newHome/payment.png",
  },
  {
    title: "Client Organizer",
    color: "bg-[#EA85FF]",
    hexColor: "#EA85FF",
    content: [
      "Privately store your client list in one place.",
      "Keep client details, and session progress securely",
      "Track your client load in one secure place",
    ],
    image: "/assets/images/newHome/organizer.png",
  },
  {
    title: "Reminders & Client Communication",
    color: "bg-[#2A1C63]",
    hexColor: "#2A1C63",
    content: [
      "No More Endless Back-and-Forth",
      "Find time to address those client whatsapps in therapy instead of spending time getting lost in the constant back and forth. ",
      "Support can never be 24/7, neither can you. We help by sharing some of the communication load. ",
    ],
    image: "/assets/images/newHome/communication.png",
  },
];

const WhyThoughtPudding: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(
    tabs.length - 1 // open last tab by default
  );
  const queryParams = useSearchParams();
    const [loginOpen, setLoginOpen] = useState(false);
    const [th_id, setTherapistId] = useState<string | null>(null);
    const [userStep, setUserStep] = useState<string | null>(null);
  
    const therapist_id = queryParams.get("q");
    const user_step = queryParams.get("step");
    const loginParam = queryParams.get("login");
  
    useEffect(() => {
      if (therapist_id && user_step) {
        setTherapistId(therapist_id);
        setUserStep(user_step);
        setLoginOpen(true);
      }
    }, [therapist_id, user_step]);
  
    useEffect(() => {
      if (loginParam) {
        setLoginOpen(true);
      }
    }, [loginParam]);
    

  return (
    <section className="max-w-6xl mx-auto py-16 px-6">
      <h2 className="text-2xl md:text-4xl lg:text-[60px] font-semibold text-center mb-14">
        Why Thought Pudding?
      </h2>

      <div className="relative z-10">
        {tabs.map((tab, index) => (
          <div
            key={index}
            className={`relative z-[${
              10 - index
            }] transition-all duration-300 ${index !== 0 ? "-mt-4" : ""} ${
              activeIndex === index ? "shadow-xl" : ""
            }`}
          >
            {/* Accordion Header */}
            <div
              className={`p-6 text-white font-semibold text-2xl ${
                tab.color
              } cursor-pointer transition-all duration-300 ${
                activeIndex === index
                  ? "rounded-t-2xl"
                  : index === tabs.length - 1
                  ? "rounded-t-2xl rounded-b-2xl"
                  : "rounded-t-2xl"
              }`}
              onClick={() =>
                setActiveIndex((prev) => (prev === index ? null : index))
              }
            >
              {tab.title}
            </div>

            {/* Accordion Content */}
            <div
              className={`overflow-hidden  transition-all duration-500 ease-in-out ${
                activeIndex === index
                  ? "max-h-[700px] opacity-100"
                  : "max-h-0 opacity-0"
              } px-10 text-black ${
                activeIndex === index ? "pt-10 pb-10 rounded-b-2xl" : ""
              }`}
              style={
                activeIndex === index
                  ? {
                      backgroundColor: "#fff",
                      borderLeft: `8px solid ${tab.hexColor}`,
                      borderRight: `8px solid ${tab.hexColor}`,
                      borderBottom: `8px solid ${tab.hexColor}`,
                    }
                  : {}
              }
            >
              <div
                className={`max-w-5xl mx-auto flex flex-col-reverse md:flex-row items-center justify-center gap-10 ${
                  index % 2 === 0 ? "md:flex-row-reverse" : ""
                }`}
              >
                <div className="lg:text-lg md:text-md text-xs md:w-1/2 md:text-left">
                  <ul className="list-disc pl-6 space-y-2">
                    {tab.content.map((point, i) => (
                      <li key={i}>{point}</li>
                    ))}
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={tab.image}
                    alt={tab.title}
                    className="max-w-[360px] w-full h-auto rounded-xl"
                  />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="w-full pt-[64px] px-4 sm:px-6 md:px-12 lg:px-20">
        <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-center justify-between gap-6 text-center md:text-left">
          {/* Text Block */}
          <div className="flex flex-col gap-4 items-center md:items-start">
            <div className="text-[32px] sm:text-[40px] font-bold text-black leading-tight">
              <span className="text-[#718FFF] md:text-black">All-in-one </span>
              <span className="relative inline-block ml-1">
                <span
                  className="absolute inset-0 bg-cover bg-center rotate-2"
                  style={{
                    backgroundImage:
                      "url('/assets/images/newHome/Hero-Title-back.png')",
                    width: "110%",
                    height: "100%",
                    transform: "rotate(0deg)",
                  }}
                ></span>
                <span
                  className={`relative text-black ${caveat.className} font-medium`}
                >
                  Practice Management
                </span>
              </span>
            </div>

            <p className="text-sm sm:text-base text-black font-medium max-w-md">
              15 day free trial. No credit card required.
            </p>
          </div>

          {/* CTA Button */}
          <div>
            <button onClick={() => setLoginOpen(true)} className="text-[13px] md:text-[20px] px-6 py-3 bg-[#251D5C] text-white rounded-lg">
              Start Free Trial
            </button>
          </div>
        </div>
      </div>
      <Login
        setLoginOpen={setLoginOpen}
        loginOpen={loginOpen}
        th_id={th_id}
        userStep={userStep}
      />
    </section>
  );
};

export default WhyThoughtPudding;
