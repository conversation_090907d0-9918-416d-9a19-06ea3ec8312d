import axiosInstance, { fetcher } from "@/utils/axios";
import endpoints from "@/utils/endpoints";
import { useMemo } from "react";
import toast from "react-hot-toast";
import useSWR from "swr";

const swrOptions = {
  revalidateOnFocus: true, // Revalidate when the component is focused
  revalidateOnReconnect: true, // Revalidate when the connection is re-established
  shouldRetryOnError: true, // Retry on error
  // revalidateIfStale: false,
  dedupingInterval: 0, // No deduplication, always fetch from the API
  refreshInterval: 0, // No interval-based revalidation (fetch only on demand)
};

type ScheduleData = {
  appointments: Array<unknown>;
  appointmentsCount: number;
  allPayTracker: Array<unknown>; // Add this line
  total: number;
  values?: Array<unknown>; // Add this l
  mutatePaymentTrackerData?: () => void;
};

export interface FilterParams {
  searchStartDate?: string;
  searchEndDate?: string;
  paymentStatus?: string;
}

interface GetPaymentsParams {
  pageSize: number;
  currentPage: number;
  activeTable: string;
  debouncedSearchText: string;
  filterparams: FilterParams;
  clientId?: string;
}

// get payment listing
export function useGetPayments(params: GetPaymentsParams) {
  const {
    pageSize,
    currentPage,
    activeTable,
    debouncedSearchText,
    filterparams,
    clientId,
  } = params;
  const query =
    `pageSize=${pageSize}&pageNumber=${currentPage}&${activeTable}=true&searchText=${debouncedSearchText}&clientName=${
      clientId ?? ""
    }` +
    ((filterparams.searchStartDate && filterparams.searchEndDate) ||
    filterparams.paymentStatus
      ? `&startDate=${filterparams.searchStartDate}&endDate=${filterparams.searchEndDate}&${filterparams.paymentStatus}=true`
      : "");

  const url = `${endpoints.paymentTracker.getPaymentsByTherapist}?${query}`;

  const { data, isLoading, error, isValidating } = useSWR<ScheduleData>(
    url,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      paymentData: data?.allPayTracker || [],
      paymentCount: data?.total || 0,
      paymentLoading: isLoading,
      paymentError: error,
      paymentValidating: isValidating,
    }),
    [data?.allPayTracker, data?.total, error, isLoading, isValidating]
  );

  return memoizedValue;
}

// get activity data
export function useGetPaytracker(startDate: Date, endDate: Date) {
  const query = `startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`;
  const url = `${endpoints.paymentTracker.getStats}?${query}`;

  const { data, isLoading, error, isValidating, mutate } = useSWR<ScheduleData>(
    url,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      paymentTrackerData: data?.values || [],
      paymentTrackerLoading: isLoading,
      paymentTrackerError: error,
      paymentTrackerValidating: isValidating,
      mutatePaymentTrackerData: mutate, // Include mutate function here
    }),
    [data?.values, error, isLoading, isValidating, mutate]
  );

  return memoizedValue;
}

// update status
export async function updateStatus(selectedStatusId: string, status: string) {
  const query = `${selectedStatusId}?status=${status}`;
  const url = `${endpoints.paymentTracker.changePaytrackerStatus}/${query}`;

  try {
    const res = await axiosInstance.put(url);
    toast.success("Status updated successfully!");
    return res;
  } catch (error) {
    toast.error("Failed to update the status. Please try again.");
    throw error;
  }
}

interface FormData {
  [key: string]: string | number | boolean;
}

// Cancellation Charge status
export async function cancellationCharge(
  cancellationChargeID: string,
  formData: FormData
) {
  const query = `${cancellationChargeID}`;
  const url = `${endpoints.paymentTracker.chargeSession}/${query}`;

  const res = await axiosInstance.put(url, formData);

  return res;
}

// get template test status
export async function getTemplateTest(clientId: string, _id: string) {
  try {
    const query = `${clientId}/${_id}`;
    const url = `${endpoints.paymentTracker.sendReminder}/${query}`;
    const res = await axiosInstance.post(url);
    toast.success("Reminder Send successfully!");
    return res;
  } catch (error) {
    return error;
  }
}

// Update Payment
export async function updatePayment(paymentID: string, formData: FormData) {
  const query = `${paymentID}`;
  const url = `${endpoints.paymentTracker.updateAmount}/${query}`;

  const res = await axiosInstance.post(url, formData);

  return res;
}

// Download Payment Tracker Excel
export async function downloadPaymentTracker(params: {
  stillPending?: boolean;
  paidOnTime?: boolean;
  paidDelayed?: boolean;
  cancelled?: boolean;
  startDate?: string;
  endDate?: string;
  searchText?: string;
  clientName?: string;
}) {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();

    // Add status parameters
    if (params.stillPending) queryParams.append('stillPending', 'true');
    if (params.paidOnTime) queryParams.append('paidOnTime', 'true');
    if (params.paidDelayed) queryParams.append('paidDelayed', 'true');
    if (params.cancelled) queryParams.append('cancelled', 'true');

    // Add date parameters
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);

    // Add search parameters
    if (params.searchText) queryParams.append('searchText', params.searchText);
    if (params.clientName) queryParams.append('clientName', params.clientName);

    const url = `${endpoints.paymentTracker.download}?${queryParams.toString()}`;

    // Make request with blob response type for file download
    const response = await axiosInstance.get(url, {
      responseType: 'blob',
    });

    // Create blob and download file
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    // Create download link
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;

    // Generate filename with current date
    const currentDate = new Date().toISOString().split('T')[0];
    link.download = `payment-tracker-${currentDate}.xlsx`;

    // Trigger download
    document.body.appendChild(link);
    link.click();

    // Cleanup
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    toast.success('Payment tracker exported successfully!');
    return response;
  } catch (error) {
    console.error('Failed to download payment tracker:', error);
    toast.error('Failed to export payment tracker. Please try again.');
    throw error;
  }
}
