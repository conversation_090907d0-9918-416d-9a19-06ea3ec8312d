import { Clock } from "@phosphor-icons/react";
import { useState, useEffect, useRef, ChangeEvent } from "react";
import ReactDOM from "react-dom";

interface WorkingHoursTimePickerProps {
  className?: string;
  value?: string;
  onChange?: (value: string) => void;
  isStartTime: boolean;
  startTime?: string; // Required for end time picker to calculate options
  duration?: number; // Duration in minutes (for end time calculation)
}

const WorkingHoursTimePicker: React.FC<WorkingHoursTimePickerProps> = ({
  className,
  value,
  onChange,
  isStartTime,
  startTime,
  duration = 15, // Default to 15 minutes
}) => {
  const [selectedTime, setSelectedTime] = useState(value || "00:00");
  const [showDropdown, setShowDropdown] = useState(false);
  const [inputValue, setInputValue] = useState(value || "00:00");
  const [isValid, setIsValid] = useState(true);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownPortalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Generate times with appropriate intervals
  const generateTimes = () => {
    if (isStartTime) {
      // For start time, generate times with 15-minute intervals
      const times: string[] = [];
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 15) {
          const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
          const formattedMinute = minute < 10 ? `0${minute}` : `${minute}`;
          times.push(`${formattedHour}:${formattedMinute}`);
        }
      }
      return times;
    } else if (startTime) {
      // For end time, generate options based on start time
      const times: string[] = [];
      const [startHour, startMinute] = startTime.split(":").map(Number);
      const startTimeInMinutes = startHour * 60 + startMinute;

      // Generate 4 options with 15-minute intervals from the start time
      for (let i = 1; i <= 4; i++) {
        const totalMinutes = startTimeInMinutes + (i * 15);
        const hour = Math.floor(totalMinutes / 60) % 24;
        const minute = totalMinutes % 60;
        const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
        const formattedMinute = minute < 10 ? `0${minute}` : `${minute}`;
        times.push(`${formattedHour}:${formattedMinute}`);
      }

      // Also include the current end time if it's not in the list
      const currentEndTime = selectedTime;
      if (currentEndTime && !times.includes(currentEndTime)) {
        times.push(currentEndTime);
        // Sort the times
        times.sort((a, b) => {
          const [aHour, aMinute] = a.split(":").map(Number);
          const [bHour, bMinute] = b.split(":").map(Number);
          const aMinutes = aHour * 60 + aMinute;
          const bMinutes = bHour * 60 + bMinute;
          return aMinutes - bMinutes;
        });
      }

      return times;
    }

    return [];
  };

  const times = generateTimes();

  useEffect(() => {
    if (value !== undefined && value !== selectedTime) {
      setSelectedTime(value);
      setInputValue(value);
      setIsValid(true);
    }
  }, [value, selectedTime]);

  // If this is an end time picker, update options when start time or duration changes
  useEffect(() => {
    if (!isStartTime && startTime) {
      // Calculate new end time based on start time and duration
      const [startHour, startMinute] = startTime.split(":").map(Number);
      const startTimeInMinutes = startHour * 60 + startMinute;

      // Calculate end time based on duration
      const totalMinutes = startTimeInMinutes + duration;
      const hour = Math.floor(totalMinutes / 60) % 24;
      const minute = totalMinutes % 60;
      const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
      const formattedMinute = minute < 10 ? `0${minute}` : `${minute}`;
      const newEndTime = `${formattedHour}:${formattedMinute}`;

      // Only update if the calculated end time is different from the current one
      if (newEndTime !== selectedTime && onChange) {
        setSelectedTime(newEndTime);
        setInputValue(newEndTime);
        onChange(newEndTime);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [startTime, isStartTime, duration]);

  const handleSelectTime = (time: string, event?: React.MouseEvent) => {
    // Prevent event bubbling
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Update state immediately
    setSelectedTime(time);
    setInputValue(time);
    setIsValid(true);

    // Call onChange immediately to update parent component
    if (onChange) {
      onChange(time);
    }

    // Close dropdown immediately for better UX
    setShowDropdown(false);
  };

  const handleClickOutside = (event: MouseEvent) => {
    // Don't close if clicking on a dropdown item
    if (event.target instanceof Element) {
      const clickedOnDropdownItem = event.target.closest('.time-dropdown-item');
      if (clickedOnDropdownItem) {
        // Allow the click to complete before closing
        setTimeout(() => {
          setShowDropdown(false);
        }, 50);
        return;
      }
    }

    // Close if clicking outside the input and dropdown
    const clickedInDropdown = dropdownPortalRef.current && dropdownPortalRef.current.contains(event.target as Node);
    const clickedInInput = dropdownRef.current && dropdownRef.current.contains(event.target as Node);

    if (!clickedInDropdown && !clickedInInput) {
      setShowDropdown(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Recalculate dropdown position when window is scrolled or resized
  useEffect(() => {
    if (showDropdown) {
      const handleScrollAndResize = () => {
        calculateDropdownPosition();
      };

      // Add event listeners for scroll and resize
      window.addEventListener('scroll', handleScrollAndResize, true);
      window.addEventListener('resize', handleScrollAndResize);

      // Find any parent modal elements that might be scrollable
      const parentModal = inputRef.current?.closest('.modal-container');
      const timeSlotContainer = inputRef.current?.closest('.time-slots-container');

      if (parentModal) {
        parentModal.addEventListener('scroll', handleScrollAndResize, true);
      }

      if (timeSlotContainer) {
        timeSlotContainer.addEventListener('scroll', handleScrollAndResize, true);
      }

      // Set up a periodic recalculation to handle any edge cases
      const intervalId = setInterval(handleScrollAndResize, 300);

      return () => {
        window.removeEventListener('scroll', handleScrollAndResize, true);
        window.removeEventListener('resize', handleScrollAndResize);

        if (parentModal) {
          parentModal.removeEventListener('scroll', handleScrollAndResize, true);
        }

        if (timeSlotContainer) {
          timeSlotContainer.removeEventListener('scroll', handleScrollAndResize, true);
        }

        clearInterval(intervalId);
      };
    }
  }, [showDropdown]);

  const validateTime = (time: string): boolean => {
    // Strict validation for 24-hour format (00:00 to 23:59)
    const timeRegex = /^([0-1]\d|2[0-3]):([0-5]\d)$/;
    return timeRegex.test(time);
  };

  // Convert 12-hour format to 24-hour format
  const convert12To24 = (time12h: string): string => {
    // Check if the input is already in 24-hour format
    if (validateTime(time12h)) {
      return time12h;
    }

    // Try to parse 12-hour format with space (e.g., "2:30 PM")
    let match = time12h.match(/^(\d{1,2}):(\d{2})\s*(AM|PM)$/i);
    if (match) {
      const [hours, minutes, period] = match;
      let hour = parseInt(hours, 10);

      // Convert to 24-hour format
      if (period.toUpperCase() === 'PM' && hour < 12) {
        hour += 12;
      } else if (period.toUpperCase() === 'AM' && hour === 12) {
        hour = 0;
      }

      // Format the result
      const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
      return `${formattedHour}:${minutes}`;
    }

    // Try to parse 12-hour format without space (e.g., "2:30PM")
    match = time12h.match(/^(\d{1,2}):(\d{2})(AM|PM)$/i);
    if (match) {
      const [hours, minutes, period] = match;
      let hour = parseInt(hours, 10);

      // Convert to 24-hour format
      if (period.toUpperCase() === 'PM' && hour < 12) {
        hour += 12;
      } else if (period.toUpperCase() === 'AM' && hour === 12) {
        hour = 0;
      }

      // Format the result
      const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
      return `${formattedHour}:${minutes}`;
    }

    // Try to parse just hours and minutes (e.g., "14:30")
    match = time12h.match(/^(\d{1,2}):(\d{2})$/);
    if (match) {
      const [hours, minutes] = match;
      const hour = parseInt(hours, 10);

      // Validate hour and minute
      if (hour >= 0 && hour < 24 && parseInt(minutes, 10) >= 0 && parseInt(minutes, 10) < 60) {
        const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
        return `${formattedHour}:${minutes}`;
      }
    }

    // Try to parse just hours (e.g., "14")
    match = time12h.match(/^(\d{1,2})$/);
    if (match) {
      const hour = parseInt(match[1], 10);

      // Validate hour
      if (hour >= 0 && hour < 24) {
        const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
        return `${formattedHour}:00`;
      }
    }

    return "00:00"; // Invalid format
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Restrict input to valid time patterns only
    // Allow partial inputs during typing (like "1:", "1:3", etc.)
    const timeInputRegex = /^$|^([0-9]{1,2})(:[0-9]{0,2})?\s*(AM|PM|am|pm)?$/;

    if (timeInputRegex.test(newValue)) {
      // Allow any input during typing, but mark as invalid if it doesn't match a valid format
      setInputValue(newValue);

      // Try to convert from 12-hour to 24-hour format
      const time24h = convert12To24(newValue);

      // Check if the input is valid
      if (validateTime(time24h)) {
        setSelectedTime(time24h);
        setIsValid(true);
        if (onChange) {
          onChange(time24h);
        }
      } else {
        setIsValid(false);
      }
    }
  };

  const handleInputBlur = (e: React.FocusEvent) => {
    // Don't close dropdown if focus is moving to a dropdown item
    const relatedTarget = e.relatedTarget as Element;
    if (relatedTarget && relatedTarget.closest('.time-dropdown-item')) {
      return;
    }

    // When the input loses focus, always format the value properly
    // Try to convert the current input value
    const time24h = convert12To24(inputValue);

    if (validateTime(time24h)) {
      // If conversion succeeded, use the converted value
      setSelectedTime(time24h);
      setInputValue(time24h);
      setIsValid(true);
      if (onChange) {
        onChange(time24h);
      }
    } else {
      // If conversion failed, reset to the last valid value
      setInputValue(selectedTime);
      setIsValid(true);
      // Ensure the onChange is called with the valid value
      if (onChange) {
        onChange(selectedTime);
      }
    }

    // Hide dropdown after a short delay to allow for click events
    setTimeout(() => {
      setShowDropdown(false);
    }, 100);
  };

  const calculateDropdownPosition = () => {
    if (inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;

      // Simple positioning logic that works consistently
      const dropdownHeight = 200;
      const dropdownWidth = Math.max(rect.width, 200);

      // Position dropdown below input by default
      let top = rect.bottom + 2;
      let left = rect.left;

      // Ensure dropdown doesn't go off screen
      if (left + dropdownWidth > viewportWidth) {
        left = viewportWidth - dropdownWidth - 10;
      }

      if (left < 10) {
        left = 10;
      }

      // If not enough space below, position above
      if (top + dropdownHeight > viewportHeight) {
        top = rect.top - dropdownHeight - 2;
        if (top < 10) {
          top = 10;
        }
      }

      setDropdownPosition({
        top,
        left,
        width: dropdownWidth
      });
    }
  };

  const handleInputFocus = () => {
    calculateDropdownPosition();
    setShowDropdown(true);
  };

  // Format time to 12-hour format for display
  const formatTimeFor12Hour = (time: string): string => {
    const [hour, minute] = time.split(":").map(Number);
    const period = hour >= 12 ? "PM" : "AM";
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minute < 10 ? '0' + minute : minute} ${period}`;
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <div className="flex items-center justify-between">
        {isStartTime ? (
          // For start time, allow input
          <>
            <div className="relative w-full">
              <input
                ref={inputRef}
                type="text"
                value={isValid && inputValue ? formatTimeFor12Hour(inputValue) : (inputValue || "")}
                onChange={handleInputChange}
                onFocus={handleInputFocus}
                onBlur={handleInputBlur}
                placeholder="12:00 AM"
                className={`w-full text-sm py-3 px-2.5 border rounded-lg outline-none focus:border-blue-600 ${
                  isValid
                    ? (!inputValue || inputValue === "00:00")
                      ? "text-gray-400 border-gray-300"
                      : "text-gray-900 border-[#D9D9D9]"
                    : "border-red-500 text-red-500"
                }`}
                style={{ minHeight: '42px' }}
              />
              <button
                type="button"
                onClick={() => {
                  calculateDropdownPosition();
                  setShowDropdown(!showDropdown);
                }}
                className="absolute right-2 top-1/2 transform -translate-y-1/2"
              >
                <Clock size={20} className="text-primary" />
              </button>
            </div>
          </>
        ) : (
          // For end time, display as disabled and calculated automatically
          <div className="relative w-full">
            <input
              ref={inputRef}
              type="text"
              value={isValid && inputValue ? formatTimeFor12Hour(inputValue) : (inputValue || "")}
              disabled={true}
              placeholder="12:00 AM"
              className={`w-full text-sm py-3 px-2.5 border rounded-lg outline-none bg-gray-100 cursor-not-allowed ${
                isValid
                  ? (!inputValue || inputValue === "00:00")
                    ? "text-gray-400 border-gray-300"
                    : "text-gray-700 border-[#D9D9D9]"
                  : "border-red-500 text-red-500"
              }`}
              style={{ minHeight: '42px' }}
            />
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
              <Clock size={20} className="text-gray-400" />
            </div>
          </div>
        )}
      </div>

      {/* Show dropdown for both start and end time - rendered in a portal to avoid being cut off */}
      {showDropdown && typeof document !== 'undefined' && ReactDOM.createPortal(
        <div
          ref={dropdownPortalRef}
          className="fixed max-h-48 overflow-y-auto z-[999999] bg-white shadow-lg border border-gray-200 rounded-lg transition-all duration-200"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`,
            maxHeight: '200px',
            position: 'fixed',
            transform: 'translateZ(0)',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
            // iOS-specific styles
            ...(/iPad|iPhone|iPod/.test(navigator.userAgent) && {
              WebkitOverflowScrolling: 'touch',
              WebkitTransform: 'translate3d(0, 0, 0)',
              backfaceVisibility: 'hidden',
              perspective: '1000px',
              willChange: 'transform'
            })
          }}
        >
          <div className="max-h-48 overflow-y-auto py-1">
            {times.length > 0 ? (
              times.map((time: string) => (
                <button
                  type="button"
                  key={time}
                  onMouseDown={(e) => {
                    handleSelectTime(time, e);
                  }}
                  className={`time-dropdown-item w-full text-left px-4 py-2 cursor-pointer hover:bg-gray-100 transition duration-200 text-sm ${
                    selectedTime === time
                      ? "bg-blue-600/10 text-blue-600 font-medium"
                      : "text-gray-900"
                  }`}
                  style={{
                    // iOS-specific button styles
                    ...(/iPad|iPhone|iPod/.test(navigator.userAgent) && {
                      WebkitTapHighlightColor: 'transparent',
                      WebkitTouchCallout: 'none',
                      minHeight: '44px' // iOS recommended touch target size
                    })
                  }}
                >
                  {formatTimeFor12Hour(time)}
                </button>
              ))
            ) : (
              <div className="px-4 py-2 text-sm text-gray-500">No time options available</div>
            )}
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default WorkingHoursTimePicker;
