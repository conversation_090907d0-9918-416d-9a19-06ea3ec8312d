/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    BASE_API_URL: process.env.NEXT_APP_BASE_API_URL,
    RAZORPAY_KEY: process.env.NEXT_APP_RAZORPAY_KEY,
    RAZORPAY_SECRET_KEY: process.env.NEXT_APP_RAZORPAY_SECRET,
    NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'thoughtpudding-data.s3.ap-south-1.amazonaws.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'thoughtpudding-public.s3.ap-south-1.amazonaws.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'app.thoughtpudding.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'dev.thoughtpudding.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
};

export default nextConfig;
