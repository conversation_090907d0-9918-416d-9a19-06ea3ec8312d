"use client";

import { WorkingHoursSlot } from "@/services/public-calendar.service";

interface TimeSlotSelectorProps {
  selectedDate: Date | null;
  availableTimeSlots: WorkingHoursSlot[];
  selectedTimeSlot: WorkingHoursSlot | null;
  onTimeSlotSelect: (slot: WorkingHoursSlot) => void;
  formatDate: (date: Date) => string;
  convertTo12Hour: (time24: string) => string;
  isLoadingSlots: boolean;
  isMobile?: boolean;
  isSlotBooked?: (slot: WorkingHoursSlot) => boolean;
}

export default function TimeSlotSelector({
  selectedDate,
  availableTimeSlots,
  selectedTimeSlot,
  onTimeSlotSelect,
  formatDate,
  convertTo12Hour,
  isLoadingSlots,
  isMobile = false,
  isSlotBooked,
}: TimeSlotSelectorProps) {

  // Function to check if a time slot is in the past
  const isPastSlot = (slot: WorkingHoursSlot, selectedDate: Date): boolean => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const slotDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate());

    // If the selected date is not today, the slot is not in the past
    if (slotDate.getTime() !== today.getTime()) {
      return false;
    }

    // If it's today, check if the slot start time has passed
    const [hours, minutes] = slot.startTime.split(':').map(Number);
    const slotDateTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), hours, minutes);

    return slotDateTime < now;
  };


  if (!selectedDate) {
    return (
      <div className="h-full flex items-center justify-center text-gray-500 py-20">
        <p className="text-lg">
          Please select a date to view available time slots
        </p>
      </div>
    );
  }

  // If we have a selected date but slots are still loading, show loading indicator
  if (isLoadingSlots) {
    return (
      <div className="h-full flex items-center justify-center py-16">
        <div className="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#718FFF] mb-2"></div>
        <p className="ml-3 text-gray-500 text-lg">
          Loading available slots...
        </p>
      </div>
    );
  }

  if (isMobile) {
    return (
      <div className="mb-6">
        <h3 className="text-[#251D5C] font-semibold mb-3 gilmer-bold">
          {formatDate(selectedDate)} ( {availableTimeSlots.length} Slot
          {availableTimeSlots.length !== 1 ? "s" : ""} Available )
        </h3>
        {availableTimeSlots.length > 0 ? (
          <div className="grid grid-cols-2 gap-2">
            {availableTimeSlots.map((slot, index) => {
              const isBooked = isSlotBooked ? isSlotBooked(slot) : false;
              const isPast = isPastSlot(slot, selectedDate);
              const isDisabled = isBooked || isPast;
              const isSelected = selectedTimeSlot && selectedTimeSlot._id === slot._id;

              return (
                <div
                  key={index}
                  className={`border rounded-lg py-2 px-3 text-center text-sm transition-all relative group
                    ${
                      isBooked
                        ? "border-red-400 bg-red-100 text-red-600 cursor-not-allowed"
                        : isPast
                        ? "border-gray-400 bg-gray-100 text-gray-500 cursor-not-allowed"
                        : isSelected
                        ? "border-[#2C58BB] bg-[#2C58BB] font-medium text-white cursor-pointer"
                        : "border-gray-200 hover:border-[#718FFF] hover:bg-[#718FFF]/5 text-[#251D5C] bg-gray-50 cursor-pointer"
                    }`}
                  onClick={() => !isDisabled && onTimeSlotSelect(slot)}
                  title={isBooked ? "Slot is already booked" : isPast ? "This time slot has passed" : ""}
                >
                  {`${convertTo12Hour(slot.startTime)} - ${convertTo12Hour(slot.endTime)}`}
                  {isBooked && (
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                      Slot is already booked
                    </div>
                  )}
                  {isPast && !isBooked && (
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                      This time slot has passed
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <p className="text-gray-500 text-center py-4">
            No available slots for this date
          </p>
        )}
      </div>
    );
  }

  return (
    <div className="w-[60%] bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
      <h3 className="text-lg font-medium mb-5 gilmer-medium">
        {formatDate(selectedDate)}{" "}
        <span className="text-gray-500 text-base">
          ( {availableTimeSlots.length} Slot
          {availableTimeSlots.length !== 1 ? "s" : ""} Available )
        </span>
      </h3>
      {availableTimeSlots.length > 0 ? (
        <div className="grid grid-cols-3 gap-3">
          {availableTimeSlots.map((slot, index) => {
            const isBooked = isSlotBooked ? isSlotBooked(slot) : false;
            const isPast = isPastSlot(slot, selectedDate);
            const isDisabled = isBooked || isPast;
            const isSelected = selectedTimeSlot && selectedTimeSlot._id === slot._id;

            return (
              <div
                key={index}
                className={`border rounded-lg py-2 px-2 text-center text-sm transition-all relative group
                  ${
                    isBooked
                      ? "border-red-400 bg-red-100 text-red-600 cursor-not-allowed"
                      : isPast
                      ? "border-gray-400 bg-gray-100 text-gray-500 cursor-not-allowed"
                      : isSelected
                      ? "border-[#2C58BB] bg-[#2C58BB] font-medium text-white cursor-pointer"
                      : "border-gray-200 hover:border-[#718FFF] hover:bg-[#718FFF]/5 cursor-pointer"
                  }`}
                onClick={() => !isDisabled && onTimeSlotSelect(slot)}
                title={isBooked ? "Slot is already booked" : isPast ? "This time slot has passed" : ""}
              >
                {`${convertTo12Hour(slot.startTime)} - ${convertTo12Hour(slot.endTime)}`}
                {isBooked && (
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                    Slot is already booked
                  </div>
                )}
                {isPast && !isBooked && (
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                    This time slot has passed
                  </div>
                )}
              </div>
            );
          })}
        </div>
      ) : (
        <div className="h-full flex items-center justify-center text-gray-500 py-16">
          <p className="text-lg">
            No available slots for this date
          </p>
        </div>
      )}
    </div>
  );
}
